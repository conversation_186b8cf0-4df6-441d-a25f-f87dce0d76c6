import frappe
from frappe.model.document import Document


@frappe.whitelist()
def sync_allow_sales_from_observation_template():
    templates = frappe.get_all(
        "Observation Template", fields=["name", "item_code", "is_billable"]
    )

    updated = []
    for template in templates:
        item_code = template.item_code
        is_billable = template.is_billable

        if not item_code:
            continue

        try:
            item = frappe.get_doc("Item", item_code)
            # Only update if value is different
            if item.is_sales_item != is_billable:
                item.is_sales_item = is_billable
                item.save()
                updated.append(item_code)
        except frappe.DoesNotExistError:
            frappe.log_error(f"Item not found: {item_code}")

    frappe.db.commit()
    return f"✔ Updated Allow Sales for {len(updated)} items: {', '.join(updated)}"
