{% set filter_fields = filter_fields or [] %}
{% set ignore_filter_fields = ignore_filter_fields or [] %}

{% set filters = filters or {} %}
{% set page_length = page_length or 25 %}

{% set meta = frappe.get_meta(doctype_name) %}
{% set filter_list = filter_list or None %}
{% set include_field = include_field or [] %}

{% if meta.get("filters") %}
    {% for filter in meta.filters %}
        {% set _ = filters.update({filter.fieldname: filter.default}) %}
    {% endfor %}
{% endif %}

{% set instances = frappe.get_list(doctype_name, filters=filter_list, fields=["*"], limit_page_length=page_length) %}

<div class="d-flex justify-content-between align-items-center mb-2 p-2 workstation-list">
    <h3 class="m-0">
        {{ doctype_name }} List

        {% if filter_list %}
        {% set filters = filter_list or {} %}
        <div class="position-relative d-inline-block ms-2 filter-badge-wrapper">
            <span class="badge rounded-pill bg-warning text-dark filter-badge">
                {{ filter_list | length }} Filter{{ "s" if filter_list|length > 1 else "" }} Applied
            </span>

            <div class="filter-hover-box">
                {% for key, value in filters.items() %}
                    <div class="filter-line">
                        <span class="filter-key">{{ key }}:</span>
                        <span class="filter-value">{{ value }}</span>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </h3>
    
    <div class="d-flex flex-column align-items-end">
        <div class="d-flex align-items-center">
            <div style="margin-right: 8px;">
                <button class="btn btn-sm btn-secondary mb-1"
                    onclick="window.workstationInstance.loadSection('{{ template }}', '{{ section }}', { doctype: '{{ doctype }}', filters: {{ filters|safe }} })">
                    <i class="fa fa-refresh text-small text-muted"></i>
                </button>
            </div>
            <div>
                <button class="btn btn-sm btn-secondary mb-1" 
                    onclick="window.location.href='/app/{{ doctype_name | replace(' ', '-') | lower }}';">
                    Advance List
                </button>
            </div>
        </div>
        <div class="text-muted text-small">
            Showing {{ instances | length }} {{ doctype_name }}
        </div>
    </div>
    
</div>

<div id="list-filter-section" class="filter-flex-container"></div>

{% if instances %}
    <div class="frappe-list border rounded list-container">
        <div class="list-row-head text-muted d-flex border-bottom p-2 bg-light sticky-top list-header">
            {% for included_field in include_field %}
                <div class="list-header-subject text-truncate text-small">
                    {{ included_field | capitalize }}
                </div>
            {% endfor %}
            {% for field in meta.fields if field.in_list_view %}
                <div class="list-header-subject text-truncate text-small">
                    {{ field.label }}
                </div>
            {% endfor %}
        </div>

        <div class="list-row-container">
            {% for instance in instances %}
                <div class="list-row d-flex align-items-center p-2 border-bottom list-row-item"
                     onclick="window.workstationInstance.renderForm('{{ doctype_name }}', '{{ instance.name }}', '#content-section');">
                    {% for included_field in include_field %}
                        <div class="list-row-col text-truncate">{{ instance[included_field] }}</div>
                    {% endfor %}
                    {% for field in meta.fields if field.in_list_view %}
                        {% set field_value = instance.get(field.fieldname, '') %}
                        <div class="list-row-col text-truncate">
                            {% if field.fieldtype == "Link" and field_value %}
                                <a href="javascript:void(0);" 
                                   onclick="window.workstationInstance.renderForm('{{ field.options }}', '{{ field_value }}', '{{ field_value }}')" 
                                   class="text-dark link-field">
                                    {{ field_value }}
                                </a>
                            {% else %}
                                <span class="text-muted">{{ field_value or "-" }}</span>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% endfor %}
        </div>
    </div>
{% else %}
    <div class="card d-flex justify-content-center align-items-center mt-3 no-records-card">
        <p class="text-muted text-center m-0">No records found.</p>
    </div>
{% endif %}

{% set total_instances = frappe.db.count(doctype_name, filters=filter_list) %}
{% set page_count = (total_instances // 25) + (1 if total_instances % 25 else 0) %}

<div class="d-flex justify-content-center mt-3 flex-column align-items-center pagination-container">
    <div class="pagination-buttons">
        {% for i in range(1, page_count + 1) %}
            <button class="btn btn-sm btn-outline-secondary pagination-btn"
                onclick="window.workstationInstance.loadSection('{{ template }}', '{{ section }}', {'doctype': '{{ doctype }}', 'filters': {{ filters }}, 'page_length': '{{ i * 25 }}'})">
                {{ i * 25 }}
            </button>
        {% endfor %}
    </div>
    <div class="text-muted p-1">
        Total {{ total_instances }}
    </div>
</div>


<script>
    function generateFilters(doctype, ignore_fields) {
        const container = document.getElementById("list-filter-section");
        container.innerHTML = "";
        container.classList.add("filter-flex-container");
    
        const initialFilters = {{ filters | tojson | safe }};
        const filterState = { ...initialFilters };
    
        function haveFiltersChanged(newFilters, oldFilters) {
            const newKeys = Object.keys(newFilters);
            const oldKeys = Object.keys(oldFilters);
            if (newKeys.length !== oldKeys.length) return true;
            return newKeys.some(key => newFilters[key] !== oldFilters[key]);
        }
    
        function handleFilterChange(fieldname, control) {
            const value = control.get_value();
    
            if (value) {
                filterState[fieldname] = value;
            } else {
                delete filterState[fieldname];
            }
    
            if (haveFiltersChanged(filterState, initialFilters)) {
                window.workstationInstance.loadSection(
                    "{{ template }}",
                    "{{ section }}",
                    {
                        doctype: "{{ doctype }}",
                        filters: filterState
                    }
                );
            }
        }
    
        frappe.model.with_doctype(doctype, () => {
            const meta = frappe.get_meta(doctype);
            const filterable_fields = meta.fields.filter(df =>
                (df.in_standard_filter || df.in_list_view) && !ignore_fields.includes(df.fieldname)
            );
    
            if (!filterable_fields.some(f => f.fieldname === 'name')) {
                filterable_fields.unshift({
                    fieldname: 'name',
                    label: 'ID',
                    fieldtype: 'Data'
                });
            }
    
            filterable_fields.forEach(df => {
                const field_wrapper = document.createElement("div");
                field_wrapper.className = "filter-item";
    
                if (df.fieldtype === "Data") {
                    const $input = $('<input type="text" class="form-control input-sm" />');
                    $input.attr('placeholder', df.label || 'ID');
    
                    const $btn = $(`
                        <button class="btn btn-outline-primary" type="button" title="Search">
                            <i class="fa fa-search"></i>
                        </button>
                    `);
    
                    const $inputGroupAppend = $('<div class="input-group-append"></div>').append($btn);
                    const $inputGroup = $('<div class="input-group input-group-sm"></div>')
                                        .append($input)
                                        .append($inputGroupAppend);
    
                    $(field_wrapper).append($inputGroup);

                    const dataControl = {
                        get_value: () => $input.val(),
                        set_value: (val) => $input.val(val)
                    };
    
                    $btn.on("click", () => handleFilterChange(df.fieldname, dataControl));
                    $input.on("keypress", (e) => { // Allow pressing Enter to search
                        if (e.which === 13) {
                            handleFilterChange(df.fieldname, dataControl);
                        }
                    });
    
                    // Pre-fill from initial filters for this custom control
                    if (initialFilters[df.fieldname]) {
                        dataControl.set_value(initialFilters[df.fieldname]);
                    }
    
                } else {
                    const control = frappe.ui.form.make_control({
                        df: {
                            fieldtype: df.fieldtype,
                            fieldname: df.fieldname,
                            options: df.options || "",
                            label: (df.fieldtype === "Check") ? df.label : "",
                            placeholder: df.label,
                            input_class: "input-sm",
                            change: () => handleFilterChange(df.fieldname, control)
                        },
                        parent: field_wrapper,
                        render_input: true
                    });
    
                    if (["Date", "DateTime", "Time"].includes(df.fieldtype)) {
                        setTimeout(() => {
                            control.$input.on("change", () => handleFilterChange(df.fieldname, control));
                        });
                    }
    
                    if (initialFilters[df.fieldname]) {
                        control.set_value(initialFilters[df.fieldname]);
                    }
                }
                container.appendChild(field_wrapper);
            });
        });
    }
    
    // Run on page load
    (function () {
        if (document.querySelector('.workstation-list')) {
            generateFilters('{{ doctype_name }}', {{ ignore_filter_fields | tojson | safe }});
        }
    })();
    </script>

<style>
.filter-item .form-control,
.filter-item .input-group-sm .form-control,
.filter-item .btn {
    height: calc(1.5em + 0.75rem + 2px); 
    box-sizing: border-box; 
}

.filter-item .input-group-append .btn {
    padding: 0.375rem 0.5rem; 
    height: auto; 
}
.filter-item .frappe-control {
    width: 100%;
}

.filter-item {
    display: flex; 
    align-items: center; 
}

.filter-item .control-label {
    margin-bottom: 0; 
    line-height: 1; 
}

.filter-item .input-group {
    width: 100%;
}

.filter-flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
    position: relative;
  
}

.filter-item {
    flex: 1 1 calc(16.66% - 0.75rem);
    min-width: 140px;
    max-width: 240px;
}

.filter-item .form-control {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
}

/* Filter Badge Styles */
.filter-badge-wrapper {
    position: relative;
    display: inline-block;
}

.filter-badge {
    opacity: 0.8;
    font-size: 10px;
    padding: 4px 8px;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.filter-badge:hover {
    opacity: 1;
}

.filter-hover-box {
    display: none;
    position: absolute;
    top: 110%;
    left: 0;
    background: rgba(237, 240, 234, 0.95);
    border: 1px solid #ddd;
    padding: 8px 12px;
    font-size: 11px;
    color: #656464;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    white-space: nowrap;                       
    min-width: max-content;
    backdrop-filter: blur(4px);
}

.filter-badge-wrapper:hover .filter-hover-box {
    display: block;
}

.filter-line {
    margin-bottom: 4px;
}

.filter-line:last-child {
    margin-bottom: 0;
}

.filter-key {
    font-weight: 600;
    text-transform: capitalize;
    margin-right: 6px;
}

.filter-value {
    color: #555;
}

/* List Container Styles */
.list-container {
    max-height: 450px;
    overflow-y: auto;
    overflow-x: auto;
}

.list-header {
    min-width: 800px;
    width: min-content;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
}

.list-header-subject {
    white-space: nowrap;
    min-width: 150px;
    flex: 1;
    padding-right: 15px;
    font-weight: 600;
    text-align: left;
}

.list-row-container {
    min-width: 800px;
    width: min-content;
}

.list-row-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.list-row-item:hover {
    background-color: #f8f9fa;
}

.list-row-col {
    min-width: 150px;
    flex: 1;
    padding-right: 15px;
    text-align: left;
}

.link-field {
    text-decoration: none;
    transition: color 0.2s ease;
}

.link-field:hover {
    color: #0056b3 !important;
    text-decoration: underline;
}

/* No Records Card */
.no-records-card {
    height: 25vh;
    min-height: 200px;
}

/* Pagination Styles */
.pagination-container {
    margin-left: 4rem;
    margin-right: 4rem;
}

.pagination-buttons {
    max-width: 100%;
    overflow-x: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 0;
}

.pagination-btn {
    margin: 0.25rem;
    min-width: 60px;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .filter-item {
        flex: 1 1 calc(25% - 0.75rem);
    }
    
    .pagination-container {
        margin-left: 2rem;
        margin-right: 2rem;
    }
}

@media (max-width: 768px) {
    .filter-item {
        flex: 1 1 calc(50% - 0.75rem);
    }
    
    .list-header-subject,
    .list-row-col {
        min-width: 120px;
        flex: 1;
        padding-right: 10px;
    }
    
    .pagination-container {
        margin-left: 1rem;
        margin-right: 1rem;
    }
    
    .pagination-buttons {
        justify-content: flex-start;
    }
}

@media (max-width: 500px) {
    .filter-item {
        flex: 1 1 100%;
    }
    
    .list-header-subject,
    .list-row-col {
        min-width: 100px;
        flex: 1;
        padding-right: 8px;
        font-size: 0.8rem;
    }
    
    .pagination-container {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
    }
}

/* Utility Classes */
.text-small {
    font-size: 0.875rem;
}

/* Scrollbar Styling */
.list-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
</style>